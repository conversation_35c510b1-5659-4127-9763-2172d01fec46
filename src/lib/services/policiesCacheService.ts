/**
 * Policies Cache Service
 * 
 * Provides platform-specific caching for customer policies data with automatic expiration.
 * Each platform's data is cached separately with a 1-minute expiration time.
 */

import type { CustomerPoliciesData } from '$lib/types/customer';

interface CacheEntry {
    data: CustomerPoliciesData;
    timestamp: number;
    expiresAt: number;
}

interface CacheStats {
    totalEntries: number;
    expiredEntries: number;
    hitRate: number;
    totalRequests: number;
    cacheHits: number;
}

export class PoliciesCacheService {
    private static instance: PoliciesCacheService;
    private cache = new Map<string, CacheEntry>();
    private readonly CACHE_DURATION = 60 * 60 * 1000;
    private stats = {
        totalRequests: 0,
        cacheHits: 0
    };

    private constructor() {
        // Start cleanup interval - run every 30 seconds
        setInterval(() => this.cleanup(), 30000);
    }

    /**
     * Get singleton instance
     */
    static getInstance(): PoliciesCacheService {
        if (!PoliciesCacheService.instance) {
            PoliciesCacheService.instance = new PoliciesCacheService();
        }
        return PoliciesCacheService.instance;
    }

    /**
     * Generate cache key for customer and platform
     */
    private getCacheKey(customerId: number, platformId: number | null): string {
        return `policies_${customerId}_${platformId || 'default'}`;
    }

    /**
     * Check if cache entry is still valid
     */
    private isValid(entry: CacheEntry): boolean {
        return Date.now() < entry.expiresAt;
    }

    /**
     * Get cached policies data if available and not expired
     */
    get(customerId: number, platformId: number | null): CustomerPoliciesData | null {
        this.stats.totalRequests++;
        
        const key = this.getCacheKey(customerId, platformId);
        const entry = this.cache.get(key);

        if (!entry) {
            console.log(`Cache miss for key: ${key}`);
            return null;
        }

        if (!this.isValid(entry)) {
            console.log(`Cache expired for key: ${key}`);
            this.cache.delete(key);
            return null;
        }

        this.stats.cacheHits++;
        console.log(`Cache hit for key: ${key}`);
        return entry.data;
    }

    /**
     * Store policies data in cache with expiration
     */
    set(customerId: number, platformId: number | null, data: CustomerPoliciesData): void {
        const key = this.getCacheKey(customerId, platformId);
        const now = Date.now();
        
        const entry: CacheEntry = {
            data,
            timestamp: now,
            expiresAt: now + this.CACHE_DURATION
        };

        this.cache.set(key, entry);
        console.log(`Cached data for key: ${key}, expires at: ${new Date(entry.expiresAt).toISOString()}`);
    }

    /**
     * Remove specific cache entry
     */
    remove(customerId: number, platformId: number | null): boolean {
        const key = this.getCacheKey(customerId, platformId);
        const deleted = this.cache.delete(key);
        
        if (deleted) {
            console.log(`Removed cache entry for key: ${key}`);
        }
        
        return deleted;
    }

    /**
     * Clear all cache entries
     */
    clear(): void {
        const size = this.cache.size;
        this.cache.clear();
        console.log(`Cleared ${size} cache entries`);
    }

    /**
     * Remove expired entries from cache
     */
    private cleanup(): void {
        const now = Date.now();
        let removedCount = 0;

        for (const [key, entry] of this.cache.entries()) {
            if (now >= entry.expiresAt) {
                this.cache.delete(key);
                removedCount++;
            }
        }

        if (removedCount > 0) {
            console.log(`Cleaned up ${removedCount} expired cache entries`);
        }
    }

    /**
     * Get cache statistics
     */
    getStats(): CacheStats {
        const now = Date.now();
        let expiredEntries = 0;

        for (const entry of this.cache.values()) {
            if (now >= entry.expiresAt) {
                expiredEntries++;
            }
        }

        return {
            totalEntries: this.cache.size,
            expiredEntries,
            hitRate: this.stats.totalRequests > 0 ? (this.stats.cacheHits / this.stats.totalRequests) * 100 : 0,
            totalRequests: this.stats.totalRequests,
            cacheHits: this.stats.cacheHits
        };
    }

    /**
     * Check if data exists in cache (regardless of expiration)
     */
    has(customerId: number, platformId: number | null): boolean {
        const key = this.getCacheKey(customerId, platformId);
        return this.cache.has(key);
    }

    /**
     * Get cache entry with metadata (for debugging)
     */
    getEntry(customerId: number, platformId: number | null): CacheEntry | null {
        const key = this.getCacheKey(customerId, platformId);
        return this.cache.get(key) || null;
    }

    /**
     * Force refresh - remove from cache to trigger fresh API call
     */
    forceRefresh(customerId: number, platformId: number | null): void {
        this.remove(customerId, platformId);
        console.log(`Forced refresh for customer ${customerId}, platform ${platformId}`);
    }
}

// Export singleton instance
export const policiesCacheService = PoliciesCacheService.getInstance();
